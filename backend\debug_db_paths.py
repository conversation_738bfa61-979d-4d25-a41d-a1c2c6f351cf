import os
import sqlite3


def list_tables(db_path: str) -> list[str]:
    if not os.path.exists(db_path):
        return []
    conn = sqlite3.connect(db_path)
    try:
        cur = conn.cursor()
        cur.execute("SELECT name FROM sqlite_master WHERE type='table'")
        return [row[0] for row in cur.fetchall()]
    finally:
        conn.close()


def main() -> None:
    cwd = os.getcwd()
    app_dir = os.path.abspath(os.path.join(cwd, "app"))

    paths = {
        "backend/fund_extraction.db": os.path.abspath(os.path.join(cwd, "fund_extraction.db")),
        "root/fund_extraction.db": os.path.abspath(os.path.join(cwd, "..", "fund_extraction.db")),
        "backend/app/fund_extraction.db": os.path.join(app_dir, "fund_extraction.db"),
    }

    print("CWD:", cwd)
    for label, path in paths.items():
        exists = os.path.exists(path)
        print(f"{label}: {path} exists={exists}")
        if exists:
            tables = list_tables(path)
            print(f"  tables({len(tables)}):", tables)


if __name__ == "__main__":
    main()


