#!/usr/bin/env python3
"""
Fund Data Extraction System - Non-Docker Startup Script
This script starts the application locally without Docker containers
"""

import os
import sys
import subprocess
import time
import argparse
import shutil
from pathlib import Path
from typing import Tuple, Optional

# Color output functions
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    RESET = '\033[0m'
    BOLD = '\033[1m'

def print_colored(message: str, color: str = Colors.WHITE):
    """Print colored message"""
    print(f"{color}{message}{Colors.RESET}")

def print_success(message: str):
    print_colored(f"✓ {message}", Colors.GREEN)

def print_error(message: str):
    print_colored(f"✗ {message}", Colors.RED)

def print_warning(message: str):
    print_colored(f"⚠ {message}", Colors.YELLOW)

def print_info(message: str):
    print_colored(f"ℹ {message}", Colors.CYAN)

def print_header(message: str):
    print_colored(f"\n{Colors.BOLD}{message}{Colors.RESET}", Colors.MAGENTA)

def run_command(cmd: str, cwd: Optional[str] = None, capture_output: bool = True, show_output: bool = False) -> Tuple[bool, str]:
    """Run a command and return success status and output"""
    try:
        if show_output:
            result = subprocess.run(cmd, shell=True, cwd=cwd, text=True)
            return result.returncode == 0, ""
        else:
            result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=capture_output, text=True)
            return result.returncode == 0, result.stdout + result.stderr
    except Exception as e:
        return False, str(e)

def check_command(command: str) -> bool:
    """Check if a command is available"""
    return shutil.which(command) is not None

def check_python_version() -> Tuple[bool, str]:
    """Check Python version"""
    try:
        # Check if virtual environment exists
        venv_path = Path("venv_fundextractor")
        if venv_path.exists():
            python_exe = venv_path / "Scripts" / "python.exe"
            success, output = run_command(f'"{python_exe}" --version')
        else:
            success, output = run_command("python --version")

        if success and "Python" in output:
            version_str = output.strip()
            # Extract version number
            version_parts = version_str.split()[1].split('.')
            major, minor = int(version_parts[0]), int(version_parts[1])
            if major >= 3 and minor >= 11:
                return True, version_str
            else:
                return False, f"{version_str} (requires 3.11+)"
        return False, "Python not found"
    except Exception as e:
        return False, f"Error checking Python: {e}"

def check_node_version() -> Tuple[bool, str]:
    """Check Node.js version"""
    try:
        success, output = run_command("node --version")
        if success and output.strip().startswith('v'):
            version_str = output.strip()
            # Extract version number
            version_num = version_str[1:].split('.')[0]
            if int(version_num) >= 18:
                return True, version_str
            else:
                return False, f"{version_str} (requires v18+)"
        return False, "Node.js not found"
    except Exception as e:
        return False, f"Error checking Node.js: {e}"

def check_dependencies() -> bool:
    """Check all system dependencies"""
    print_info("Checking system dependencies...")
    print()

    all_good = True

    # Check Python
    print("Python:      ", end="")
    python_ok, python_msg = check_python_version()
    if python_ok:
        print_success(python_msg)
    else:
        print_error(python_msg)
        all_good = False

    # Check pip
    print("pip:         ", end="")
    if check_command("pip"):
        print_success("Available")
    else:
        print_error("pip not found")
        all_good = False

    # Note: Node.js and npm are no longer required since we're using simple HTML frontend
    print("Frontend:    ", end="")
    if Path("test-frontend.html").exists():
        print_success("Simple HTML frontend available")
    else:
        print_error("test-frontend.html not found")
        all_good = False

    print()

    if not all_good:
        print_error("❌ Some dependencies are missing.")
        print()
        print_warning("Installation Instructions:")
        print()
        print_colored("Python 3.11+:", Colors.WHITE)
        print_colored("  Download from: https://www.python.org/downloads/", Colors.WHITE)
        print()
        return False

    print_success("✅ All dependencies are available!")
    return True

def setup_virtual_environment() -> bool:
    """Setup virtual environment if it doesn't exist"""
    venv_path = Path("venv_fundextractor")

    if not venv_path.exists():
        print_info("Creating virtual environment...")
        success, output = run_command("python -m venv venv_fundextractor")
        if not success:
            print_error("Failed to create virtual environment")
            print(output)
            return False
        print_success("✓ Virtual environment created")
    else:
        print_success("✓ Virtual environment already exists")

    return True

def setup_environment():
    """Setup environment files"""
    print_info("Setting up environment...")

    # Create .env file if it doesn't exist
    if not Path(".env").exists():
        if Path(".env.example").exists():
            shutil.copy(".env.example", ".env")
            print_success("✓ .env file created from example")
        else:
            env_content = """# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/fundextraction

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=dev-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS
ALLOWED_HOSTS=["http://localhost:3000","http://127.0.0.1:3000"]

# File Storage
UPLOAD_DIR=uploads
MAX_FILE_SIZE=104857600

# ML Models
SPACY_MODEL=en_core_web_sm
"""
            with open(".env", "w") as f:
                f.write(env_content)
            print_success("✓ Basic .env file created")
    else:
        print_success("✓ .env file already exists")

    # Create backend .env if needed
    backend_env = Path("backend/.env")
    if not backend_env.exists():
        shutil.copy(".env", "backend/.env")
        print_success("✓ backend/.env file created")

    # Force absolute runtime paths via environment variables so server and scripts
    # use the same SQLite DB and uploads directory regardless of CWD
    project_root = Path.cwd()
    backend_dir = project_root / "backend"
    db_path = (backend_dir / "fund_extraction.db").resolve()
    uploads_dir = (project_root / "uploads").resolve()

    # Ensure uploads directory exists early
    uploads_dir.mkdir(parents=True, exist_ok=True)

    # Use forward slashes for SQLAlchemy URL on Windows (avoid backslashes in f-strings)
    db_path_str = str(db_path).replace('\\\\', '/')
    db_url = "sqlite:///" + db_path_str
    os.environ["DATABASE_URL"] = db_url
    os.environ["UPLOAD_DIR"] = str(uploads_dir)

    print_info("Configured runtime paths:")
    print_colored(f"  DATABASE_URL = {db_url}", Colors.WHITE)
    print_colored(f"  UPLOAD_DIR   = {uploads_dir}", Colors.WHITE)

def install_dependencies(skip_deps: bool = False, verbose: bool = False) -> bool:
    """Install application dependencies in virtual environment"""
    if skip_deps:
        print_warning("⚠ Skipping dependency installation")
        return True

    print_info("Installing dependencies in virtual environment...")

    # Get virtual environment paths
    venv_path = Path("venv_fundextractor")
    if os.name == 'nt':  # Windows
        python_exe = venv_path / "Scripts" / "python.exe"
        pip_exe = venv_path / "Scripts" / "pip.exe"
    else:  # Unix-like
        python_exe = venv_path / "bin" / "python"
        pip_exe = venv_path / "bin" / "pip"

    # Verify virtual environment is working
    print_info("Verifying virtual environment...")
    success, output = run_command(f'"{python_exe}" --version', show_output=verbose)
    if not success:
        print_error("Virtual environment Python not working")
        return False

    # Install backend dependencies
    print_info("Installing Python dependencies...")

    # First install numpy with compatible version to avoid spacy conflicts
    print_info("Installing compatible numpy version...")
    cmd = f'"{pip_exe}" install "numpy<2.0"'
    if not verbose:
        cmd += " --quiet"

    success, output = run_command(cmd, show_output=verbose)
    if not success:
        print_error("Failed to install numpy")
        if not verbose:
            print(output)
        return False

    # Install all other dependencies
    print_info("Installing remaining dependencies...")
    cmd = f'"{pip_exe}" install -r backend/requirements.txt'
    if not verbose:
        cmd += " --quiet"

    success, output = run_command(cmd, show_output=verbose)
    if not success:
        print_error("Failed to install Python dependencies")
        if not verbose:
            print(output)
        return False

    # Download spaCy model
    print_info("Downloading spaCy model...")
    success, output = run_command(f'"{python_exe}" -m spacy download en_core_web_sm')
    if not success:
        print_warning("⚠ Failed to download spaCy model, will use fallback")

    print_success("✓ Python dependencies installed in virtual environment")

    # Skip frontend dependencies since we're using simple HTML frontend
    print_info("Using simple HTML frontend (no Node.js dependencies needed)")

    print_success("✅ All dependencies installed!")
    return True

def initialize_database(verbose: bool = False) -> bool:
    """Initialize the database"""
    print_info("Checking SQLite database...")

    # Check if database file already exists
    db_file = Path("backend/fund_extraction.db")
    if db_file.exists():
        print_success("✓ Database already exists, skipping table creation")
        
        # Just verify tables exist
        try:
            import sqlite3
            conn = sqlite3.connect(str(db_file))
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            if len(tables) >= 8:  # We expect at least 8 tables
                print_success(f"✓ Found {len(tables)} tables in database")
            else:
                print_warning(f"⚠ Only found {len(tables)} tables, may need to recreate")
                return create_database_tables(verbose)
        except Exception as e:
            print_warning(f"⚠ Could not verify database: {e}")
            return create_database_tables(verbose)
    else:
        print_info("Database not found, creating new database...")
        return create_database_tables(verbose)

    # Try to create admin user (will skip if already exists)
    return create_admin_user(verbose)

def create_database_tables(verbose: bool = False) -> bool:
    """Create database tables"""
    print_info("Creating database tables...")
    
    # Get virtual environment python path
    venv_path = Path("venv_fundextractor")
    if os.name == 'nt':  # Windows
        python_exe = venv_path / "Scripts" / "python.exe"
    else:  # Unix-like
        python_exe = venv_path / "bin" / "python"
    
    # Create tables specifically in backend/fund_extraction.db that the running
    # backend process will use by invoking a backend-aware creator script
    script_path = Path("backend") / "create_tables_backend.py"
    success, output = run_command(f'"{python_exe}" "{script_path}"', show_output=verbose)
    if success:
        print_success("✓ Database tables created successfully")
        return create_admin_user(verbose)
    else:
        print_error("Failed to create database tables")
        if verbose:
            print(output)
        return False

def create_admin_user(verbose: bool = False) -> bool:
    """Create admin user if it doesn't exist"""
    # Get virtual environment python path
    venv_path = Path("venv_fundextractor")
    if os.name == 'nt':  # Windows
        python_exe = venv_path / "Scripts" / "python.exe"
    else:  # Unix-like
        python_exe = venv_path / "bin" / "python"
    
    # Try to create admin user
    os.environ["INITIAL_ADMIN_USERNAME"] = "admin"
    os.environ["INITIAL_ADMIN_EMAIL"] = "<EMAIL>"
    os.environ["INITIAL_ADMIN_PASSWORD"] = "admin123"

    os.chdir("backend")
    success, output = run_command(f'"{python_exe}" scripts/create_admin.py', show_output=verbose)
    if success:
        print_success("✓ Admin user created (admin/admin123)")
    else:
        print_warning("⚠ Admin user creation skipped (may already exist)")
        if verbose:
            print(output)

    # Clean up environment variables
    for var in ["INITIAL_ADMIN_USERNAME", "INITIAL_ADMIN_EMAIL", "INITIAL_ADMIN_PASSWORD"]:
        os.environ.pop(var, None)

    os.chdir("..")
    return True

def kill_existing_processes():
    """Kill any existing backend processes"""
    print_info("Checking for existing processes...")
    
    if os.name == 'nt':  # Windows
        try:
            # Kill any existing uvicorn processes on port 8000
            subprocess.run([
                "powershell", "-Command",
                "Get-Process | Where-Object {$_.ProcessName -eq 'python' -and $_.CommandLine -like '*uvicorn*'} | Stop-Process -Force"
            ], capture_output=True, text=True)
            
            # Also try to kill by port
            subprocess.run([
                "powershell", "-Command", 
                "netstat -ano | findstr :8000 | ForEach-Object { $_.Split(' ')[-1] } | ForEach-Object { taskkill /PID $_ /F }"
            ], capture_output=True, text=True)
            
            print_success("✓ Cleaned up existing processes")
        except Exception as e:
            print_warning(f"⚠ Could not clean up processes: {e}")
    else:  # Unix-like
        try:
            # Kill processes using port 8000
            subprocess.run(["pkill", "-f", "uvicorn.*8000"], capture_output=True)
            print_success("✓ Cleaned up existing processes")
        except Exception as e:
            print_warning(f"⚠ Could not clean up processes: {e}")

def check_port_available(port: int) -> bool:
    """Check if a port is available"""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            return True
    except OSError:
        return False

def find_available_port(start_port: int = 8000, max_attempts: int = 10) -> int:
    """Find an available port starting from start_port"""
    for port in range(start_port, start_port + max_attempts):
        if check_port_available(port):
            return port
    return None

def get_user_port_confirmation() -> int:
    """Get user confirmation for port selection"""
    print_header("Port Selection")
    
    # Check default port first
    default_port = 8000
    if check_port_available(default_port):
        print_colored(f"✓ Port {default_port} is available", Colors.GREEN)
        print_colored("Press Enter to use default port 8000, or enter a different port number:", Colors.WHITE)
        user_input = input().strip()
        if not user_input:
            return default_port
        try:
            port = int(user_input)
            if check_port_available(port):
                return port
            else:
                print_error(f"Port {port} is not available")
                return get_user_port_confirmation()
        except ValueError:
            print_error("Invalid port number")
            return get_user_port_confirmation()
    else:
        print_warning(f"Port {default_port} is not available")
        available_port = find_available_port(default_port + 1)
        if available_port:
            print_colored(f"Found available port: {available_port}", Colors.GREEN)
            print_colored(f"Press Enter to use port {available_port}, or enter a different port number:", Colors.WHITE)
            user_input = input().strip()
            if not user_input:
                return available_port
            try:
                port = int(user_input)
                if check_port_available(port):
                    return port
                else:
                    print_error(f"Port {port} is not available")
                    return get_user_port_confirmation()
            except ValueError:
                print_error("Invalid port number")
                return get_user_port_confirmation()
        else:
            print_error("No available ports found in range 8000-8010")
            return None

def start_services():
    """Start the application services using virtual environment"""
    print_info("Starting services...")

    # Kill any existing processes first
    kill_existing_processes()

    # Wait a moment for processes to fully terminate
    time.sleep(2)

    # Get user port confirmation
    port = get_user_port_confirmation()
    if port is None:
        print_error("❌ Could not find available port. Exiting.")
        return

    # Create uploads directory (already ensured in setup_environment, but keep safe)
    uploads_dir = Path("uploads")
    uploads_dir.mkdir(parents=True, exist_ok=True)

    # Get virtual environment python path
    venv_path = Path("venv_fundextractor")
    if os.name == 'nt':  # Windows
        python_exe = venv_path / "Scripts" / "python.exe"
    else:  # Unix-like
        python_exe = venv_path / "bin" / "python"

    # Start backend using virtual environment
    print_info(f"Starting backend server on port {port}...")
    # Inherit environment (contains absolute DATABASE_URL and UPLOAD_DIR)
    if os.name == 'nt':  # Windows
        # Use absolute path and proper subprocess call for Windows
        python_exe_abs = Path(python_exe).resolve()
        subprocess.Popen([
            str(python_exe_abs), "-m", "uvicorn", "app.main:app", "--reload", "--host", "127.0.0.1", "--port", str(port)
        ], cwd="backend", creationflags=subprocess.CREATE_NEW_CONSOLE)
    else:  # Unix-like
        subprocess.Popen([
            str(python_exe), "-m", "uvicorn", "app.main:app", "--reload", "--host", "127.0.0.1", "--port", str(port)
        ], cwd="backend")

    time.sleep(5)

    # Open the simple HTML frontend in default browser
    print_info("Opening simple HTML frontend...")
    html_path = Path("test-frontend.html").absolute()
    if os.name == 'nt':  # Windows
        subprocess.run(f'start "file://{html_path}"', shell=True)
    else:  # Unix-like
        subprocess.run(["xdg-open", f"file://{html_path}"])

    print()
    print_success("🚀 Application started successfully!")
    print()
    print_header("Application URLs:")
    print_colored("Simple Frontend: file://" + str(html_path), Colors.CYAN)
    print_colored(f"Backend API:     http://localhost:{port}", Colors.CYAN)
    print_colored(f"API Docs:        http://localhost:{port}/docs", Colors.CYAN)
    print_colored(f"Health Check:    http://localhost:{port}/health", Colors.CYAN)
    print()
    print_info("Backend is running in a separate window using virtual environment.")
    print_info("Simple HTML frontend opened in your browser.")
    print_info("Close the backend window or press Ctrl+C to stop the service.")
    
    # Save port to file for frontend to read
    with open("backend_port.txt", "w") as f:
        f.write(str(port))

def main():
    parser = argparse.ArgumentParser(description="Fund Data Extraction System - Non-Docker Startup")
    parser.add_argument("--help-full", action="store_true", help="Show detailed help")
    parser.add_argument("--check-only", action="store_true", help="Only check dependencies")
    parser.add_argument("--skip-deps", action="store_true", help="Skip dependency installation")
    parser.add_argument("--verbose", action="store_true", help="Show detailed output")
    
    args = parser.parse_args()
    
    if args.help_full:
        print_header("Fund Data Extraction System - Non-Docker Startup")
        print()
        print_colored("This script starts the application locally without Docker containers.", Colors.WHITE)
        print_colored("Uses a lightweight HTML frontend instead of React for minimal resource usage.", Colors.WHITE)
        print()
        print_colored("Usage: python start_app.py [OPTIONS]", Colors.WHITE)
        print()
        print_colored("Options:", Colors.WHITE)
        print_colored("  --help-full   Show this help message", Colors.WHITE)
        print_colored("  --check-only  Only check dependencies, don't start services", Colors.WHITE)
        print_colored("  --skip-deps   Skip dependency installation", Colors.WHITE)
        print_colored("  --verbose     Show detailed output", Colors.WHITE)
        print()
        print_colored("Prerequisites:", Colors.WHITE)
        print_colored("  - Python 3.11+ with pip", Colors.WHITE)
        print_colored("  - Simple HTML frontend (test-frontend.html)", Colors.WHITE)
        print_colored("  - PostgreSQL 15+ (optional - can use SQLite)", Colors.WHITE)
        print_colored("  - Redis 7+ (optional)", Colors.WHITE)
        return
    
    print_header("Fund Data Extraction System - Non-Docker Startup")
    print()

    # Check dependencies
    if not check_dependencies():
        sys.exit(1)

    if args.check_only:
        print_success("✅ Dependency check completed!")
        return

    # Setup virtual environment
    if not setup_virtual_environment():
        print_error("❌ Failed to setup virtual environment")
        sys.exit(1)

    # Setup environment
    setup_environment()

    # Install dependencies
    if not install_dependencies(args.skip_deps, args.verbose):
        print_error("❌ Failed to install dependencies")
        sys.exit(1)

    # Initialize database
    initialize_database(args.verbose)

    # Start services
    start_services()

if __name__ == "__main__":
    main()
