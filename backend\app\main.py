from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from app.core.config import settings
from app.core.database import get_db, init_database
from app.services.cache_service import cache

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    init_database()  # Initialize SQLite database
    cache.cleanup_expired()  # Initialize cache
    yield
    # Shutdown
    cache.clear()


app = FastAPI(
    title="Fund Data Extraction API",
    description="API for extracting financial data from PDF annual reports",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers - temporarily commented out until we update them for SQLite
# app.include_router(auth.router, prefix="/api/v1")
# app.include_router(files.router, prefix="/api/v1")
# app.include_router(extractions.router, prefix="/api/v1")


@app.get("/")
async def root():
    return {"message": "Fund Data Extraction API"}


@app.get("/health")
async def health_check():
    """Health check endpoint that verifies database connectivity."""
    try:
        # Test database connection with direct SQLite
        with get_db() as conn:
            cursor = conn.execute("SELECT 1")
            cursor.fetchone()

        # Get cache statistics
        cache_stats = cache.stats()

        return {
            "status": "healthy",
            "database": "connected",
            "cache": {
                "type": "in-memory",
                "stats": cache_stats
            },
            "version": "1.0.0"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "database": "disconnected",
            "error": str(e),
            "version": "1.0.0"
        }


@app.post("/test-extraction")
async def test_extraction():
    """Test ML extraction engine availability"""
    return {
        "success": True,
        "message": "ML extraction engine is ready. Upload a PDF to see results.",
        "results": []
    }

@app.post("/shutdown")
async def shutdown_server():
    """Shutdown the server gracefully"""
    import os
    import signal
    
    # Get the current process ID
    pid = os.getpid()
    
    # Send SIGTERM to the current process
    os.kill(pid, signal.SIGTERM)
    
    return {"message": "Server shutdown initiated"}