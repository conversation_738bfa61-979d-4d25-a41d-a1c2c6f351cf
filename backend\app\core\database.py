import sqlite3
import os
from pathlib import Path
from contextlib import contextmanager

from .config import settings

# Extract SQLite database path from DATABASE_URL
def get_db_path():
    """Extract the database file path from DATABASE_URL"""
    if settings.DATABASE_URL.startswith("sqlite:///"):
        return settings.DATABASE_URL[10:]  # Remove "sqlite:///" prefix
    else:
        # Fallback to default path
        return os.path.join(os.path.dirname(__file__), '..', '..', 'fund_extraction.db')

@contextmanager
def get_db():
    """Get a SQLite database connection"""
    db_path = get_db_path()

    # Ensure the database directory exists
    Path(db_path).parent.mkdir(parents=True, exist_ok=True)

    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row  # Enable dict-like access to rows
    try:
        yield conn
    finally:
        conn.close()

def init_database():
    """Initialize the database with basic tables if they don't exist"""
    with get_db() as conn:
        # Create a simple test table to verify database works
        conn.execute("""
            CREATE TABLE IF NOT EXISTS health_check (
                id INTEGER PRIMARY KEY,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Create a simple extractions table for testing
        conn.execute("""
            CREATE TABLE IF NOT EXISTS extractions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                status TEXT NOT NULL DEFAULT 'processing',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                completed_at DATETIME,
                error_message TEXT
            )
        """)

        conn.commit()